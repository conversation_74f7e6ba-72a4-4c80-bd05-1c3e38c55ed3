import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_microsoft_stats():
    """
    Fetch and calculate comprehensive statistics for Microsoft stock (MSFT)
    """
    print("=" * 60)
    print("MICROSOFT CORPORATION (MSFT) - STOCK STATISTICS")
    print("=" * 60)

    # Initialize ticker
    msft = yf.Ticker("MSFT")

    # Get basic company info
    info = msft.info
    print(f"\n📊 COMPANY OVERVIEW")
    print(f"Company Name: {info.get('longName', 'N/A')}")
    print(f"Sector: {info.get('sector', 'N/A')}")
    print(f"Industry: {info.get('industry', 'N/A')}")
    print(f"Market Cap: ${info.get('marketCap', 0):,.0f}")
    print(f"Enterprise Value: ${info.get('enterpriseValue', 0):,.0f}")
    print(f"Employees: {info.get('fullTimeEmployees', 0):,}")

    # Get historical data for different periods
    periods = {
        '1 Month': '1mo',
        '3 Months': '3mo',
        '6 Months': '6mo',
        '1 Year': '1y',
        '2 Years': '2y'
    }

    print(f"\n💰 CURRENT PRICE INFORMATION")
    current_price = info.get('currentPrice', info.get('regularMarketPrice', 0))
    previous_close = info.get('previousClose', 0)
    day_change = current_price - previous_close
    day_change_pct = (day_change / previous_close) * 100 if previous_close > 0 else 0

    print(f"Current Price: ${current_price:.2f}")
    print(f"Previous Close: ${previous_close:.2f}")
    print(f"Day Change: ${day_change:.2f} ({day_change_pct:+.2f}%)")
    print(f"Day High: ${info.get('dayHigh', 0):.2f}")
    print(f"Day Low: ${info.get('dayLow', 0):.2f}")
    print(f"52-Week High: ${info.get('fiftyTwoWeekHigh', 0):.2f}")
    print(f"52-Week Low: ${info.get('fiftyTwoWeekLow', 0):.2f}")

    # Calculate statistics for each period
    for period_name, period_code in periods.items():
        try:
            print(f"\n📈 {period_name.upper()} STATISTICS")
            hist_data = msft.history(period=period_code)

            if hist_data.empty:
                print(f"No data available for {period_name}")
                continue

            # Calculate basic statistics
            closes = hist_data['Close']
            returns = closes.pct_change().dropna()

            # Price statistics
            start_price = closes.iloc[0]
            end_price = closes.iloc[-1]
            total_return = ((end_price - start_price) / start_price) * 100

            print(f"Start Price: ${start_price:.2f}")
            print(f"End Price: ${end_price:.2f}")
            print(f"Total Return: {total_return:+.2f}%")
            print(f"Highest Price: ${closes.max():.2f}")
            print(f"Lowest Price: ${closes.min():.2f}")
            print(f"Average Price: ${closes.mean():.2f}")

            # Volatility and risk metrics
            daily_volatility = returns.std()
            annualized_volatility = daily_volatility * np.sqrt(252) * 100  # 252 trading days

            print(f"Daily Volatility: {daily_volatility:.4f}")
            print(f"Annualized Volatility: {annualized_volatility:.2f}%")

            # Additional statistics
            print(f"Best Day: {returns.max()*100:+.2f}%")
            print(f"Worst Day: {returns.min()*100:+.2f}%")
            print(f"Positive Days: {(returns > 0).sum()}/{len(returns)} ({(returns > 0).mean()*100:.1f}%)")

            # Volume statistics
            avg_volume = hist_data['Volume'].mean()
            print(f"Average Volume: {avg_volume:,.0f}")

        except Exception as e:
            print(f"Error calculating {period_name} statistics: {e}")

    # Financial ratios and metrics
    print(f"\n💼 FINANCIAL METRICS")
    print(f"P/E Ratio: {info.get('trailingPE', 'N/A')}")
    print(f"Forward P/E: {info.get('forwardPE', 'N/A')}")
    print(f"PEG Ratio: {info.get('pegRatio', 'N/A')}")
    print(f"Price-to-Book: {info.get('priceToBook', 'N/A')}")
    print(f"Price-to-Sales: {info.get('priceToSalesTrailing12Months', 'N/A')}")
    print(f"Dividend Yield: {info.get('dividendYield', 0)*100:.2f}%" if info.get('dividendYield') else "Dividend Yield: N/A")
    print(f"Beta: {info.get('beta', 'N/A')}")

    # Analyst recommendations
    print(f"\n🎯 ANALYST INFORMATION")
    print(f"Target Price: ${info.get('targetMeanPrice', 0):.2f}")
    print(f"Recommendation: {info.get('recommendationKey', 'N/A').title()}")
    print(f"Number of Analysts: {info.get('numberOfAnalystOpinions', 'N/A')}")

    print(f"\n" + "=" * 60)
    print(f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"=" * 60)

if __name__ == '__main__':
    get_microsoft_stats()